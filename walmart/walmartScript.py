from curl_cffi import requests
# import requests
from bs4 import BeautifulSoup  # Import BeautifulSoup


def main():
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q.8',
        'accept-language': 'zh-CN,zh;q.8,zh-TW;q.7,zh-HK;q.5,en-US;q.3,en;q.2',
        'cache-control': 'max-age=0',
        'downlink': '10',
        'dpr': '1',
        'priority': 'u=0, i',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    }

    proxy_url = 'http://127.0.0.1:10809'

    try:
        response = requests.get(
            'https://www.walmart.com/ip/9677913892',
            # Changed URL to the specific one you provided
            headers=headers,
            proxies={'http': proxy_url, 'https': proxy_url},
            impersonate="chrome120",
            timeout=10
        )
        response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
        print(response.text)

        soup = BeautifulSoup(response.text, 'html.parser')

        # 1. 主图 (Main Image)
        main_image = None
        # Try to find from og:image meta tag first (more reliable)
        og_image_tag = soup.find('meta', property='og:image')
        if og_image_tag:
            main_image = og_image_tag.get('content')
        else:  # Fallback to preload link
            preload_image_link = soup.find('link', rel='preload', as_='image',
                                           href=lambda href: 'walmartimages.com' in str(href) and 'seo' in str(href))
            if preload_image_link:
                main_image = preload_image_link.get('href').split('?')[0]  # Remove query params if any

        # 2. 品牌 (Brand)
        brand = None
        brand_tag = soup.find('a', {'data-testid': 'brand-link'})  # Look for brand link
        if brand_tag:
            brand = brand_tag.text.strip()
        else:  # Try schema.org data
            schema_product = soup.find('script', {'type': 'application/ld+json', 'data-seo-id': 'schema-org-product'})
            if schema_product:
                import json
                try:
                    product_data = json.loads(schema_product.string)
                    if product_data and 'brand' in product_data and 'name' in product_data['brand']:
                        brand = product_data['brand']['name']
                except json.JSONDecodeError:
                    pass  # Handle JSON parsing error

        # 3. 标题 (Title)
        title = None
        title_tag = soup.find('h1', {'data-testid': 'product-title'})  # Standard title tag
        if title_tag:
            title = title_tag.text.strip()
        else:  # Fallback to og:title meta tag
            og_title_tag = soup.find('meta', property='og:title')
            if og_title_tag:
                title = og_title_tag.get('content')
            elif schema_product:  # Fallback to schema.org product name
                import json
                try:
                    product_data = json.loads(schema_product.string)
                    if product_data and 'name' in product_data:
                        title = product_data['name']
                except json.JSONDecodeError:
                    pass

        # 4. 评分 (Rating)
        rating = None
        # The rating is often in a div with a specific data-attribute or class
        rating_span = soup.find('span', {'data-testid': 'rating-badge'})
        if rating_span:
            rating = rating_span.text.strip()
            if rating:
                try:
                    rating = float(rating.split(' ')[0])  # Assuming format like "4.6 out of 5 stars"
                except ValueError:
                    rating = None
        else:  # Try finding it in the structured data (schema.org) if present
            schema_product = soup.find('script', {'type': 'application/ld+json', 'data-seo-id': 'schema-org-product'})
            if schema_product:
                import json
                try:
                    product_data = json.loads(schema_product.string)
                    if product_data and 'aggregateRating' in product_data and 'ratingValue' in product_data[
                        'aggregateRating']:
                        rating = float(product_data['aggregateRating']['ratingValue'])
                except (json.JSONDecodeError, ValueError):
                    pass

        # 5. 评论数量 (Number of Reviews)
        num_reviews = None
        reviews_span = soup.find('span', {'data-testid': 'customer-reviews-count'})
        if reviews_span:
            reviews_text = reviews_span.text.strip().replace('(', '').replace(')', '').split(' ')[
                0]  # E.g., "(1,234 reviews)"
            try:
                num_reviews = int(reviews_text.replace(',', ''))
            except ValueError:
                num_reviews = None
        else:  # Try schema.org
            schema_product = soup.find('script', {'type': 'application/ld+json', 'data-seo-id': 'schema-org-product'})
            if schema_product:
                import json
                try:
                    product_data = json.loads(schema_product.string)
                    if product_data and 'aggregateRating' in product_data and 'reviewCount' in product_data[
                        'aggregateRating']:
                        num_reviews = int(product_data['aggregateRating']['reviewCount'])
                except (json.JSONDecodeError, ValueError):
                    pass

        # 6. 价格 (Price)
        price = None
        # Look for the main price element, often with a specific class or data-testid
        price_span = soup.find('span', {'data-testid': 'price'})
        if price_span:
            price_text = price_span.find('span', class_='w_rP').text.strip()  # Example class, may vary
            if price_text:
                try:
                    price = float(price_text.replace('$', '').replace(',', ''))
                except ValueError:
                    price = None
        else:  # Try schema.org data (most reliable for price)
            schema_product = soup.find('script', {'type': 'application/ld+json', 'data-seo-id': 'schema-org-product'})
            if schema_product:
                import json
                try:
                    product_data = json.loads(schema_product.string)
                    if product_data and 'offers' in product_data and len(product_data['offers']) > 0:
                        offer = product_data['offers'][0]
                        if 'price' in offer:
                            price = float(offer['price'])
                except (json.JSONDecodeError, ValueError):
                    pass

        # 7. 卖家 (Seller)
        seller = None
        seller_span = soup.find('span', string='Sold and shipped by')  # Look for the "Sold and shipped by" text
        if seller_span:
            seller_link = seller_span.find_next('a')  # Seller name is often in an <a> tag after this text
            if seller_link:
                seller = seller_link.text.strip()
            else:  # Sometimes it's just text
                seller_text_container = seller_span.find_next('span', class_='w_rP')  # Example class
                if seller_text_container:
                    seller = seller_text_container.text.strip()
        else:  # Check for common marketplace classes/attributes if "Sold and shipped by" isn't present
            marketplace_seller_info = soup.find('div', class_='seller-info-module')  # Example, class may vary
            if marketplace_seller_info:
                seller_name_tag = marketplace_seller_info.find('a', class_='seller-name')  # Example, class may vary
                if seller_name_tag:
                    seller = seller_name_tag.text.strip()
                elif 'Walmart' in response.text:  # If no specific seller found, assume Walmart
                    seller = "Walmart"

        # 8. 配送 (Shipping) - often same as seller unless "Fulfilled by Walmart"
        # This can be complex. For simplicity, we'll assume "Wlarmer" (Walmart) for now if sold by them.
        # More robust parsing would involve looking at shipping options/blocks on the page.
        shipping = seller  # Default to seller for now

        # 9 & 10. 变体 (Variants) - This is highly dynamic and requires more advanced parsing.
        # Walmart's product variations (size, color, etc.) are usually loaded via JavaScript
        # or are complex nested HTML structures. Extracting them robustly often requires
        # inspecting network requests or using a headless browser.
        variants = []  # Placeholder

        # 11. 变体ID (Variant ID) - Also part of variant data, typically within JavaScript.
        variant_id = None  # Placeholder

        # 12. 到达时间 (Estimated Delivery Time) - Very dynamic, based on location.
        # Requires locating the specific delivery estimate element.
        delivery_time = None  # Placeholder

        # 13. 库存 (Stock) - Usually indicated by "In Stock," "Out of Stock," etc.
        stock = None
        # Look for availability message or button states
        availability_div = soup.find('div', {'data-testid': 'item-product-availability'})
        if availability_div:
            stock = availability_div.text.strip()
        elif soup.find('button', {'data-tl-id': 'add-to-cart-button', 'disabled': True}):  # If Add to Cart is disabled
            stock = "Out of Stock"
        elif soup.find('button', {'data-tl-id': 'add-to-cart-button', 'disabled': False}):  # If Add to Cart is enabled
            stock = "In Stock"
        else:  # Try schema.org
            schema_product = soup.find('script', {'type': 'application/ld+json', 'data-seo-id': 'schema-org-product'})
            if schema_product:
                import json
                try:
                    product_data = json.loads(schema_product.string)
                    if product_data and 'offers' in product_data and len(product_data['offers']) > 0:
                        offer = product_data['offers'][0]
                        if 'availability' in offer:
                            # availability will be a URL like 'https://schema.org/InStock'
                            stock_status = offer['availability'].split('/')[-1]
                            stock = stock_status  # e.g., "InStock", "OutOfStock"
                except (json.JSONDecodeError, ValueError):
                    pass

        # 14. 类目 (Category) - Often in breadcrumbs
        category = []
        breadcrumbs = soup.find('nav', {'aria-label': 'Breadcrumbs'})
        if breadcrumbs:
            category_links = breadcrumbs.find_all('li', class_='breadcrumb-list-item')  # Or similar class
            category = [link.text.strip() for link in category_links if link.text.strip()]

        # 15. 划线价/原价 (Strikethrough/Original Price)
        original_price = None
        # Look for elements indicating a compare price or original price when there's a sale
        # This often involves finding the current price, then looking for a sibling element.
        # Example: <span class="price-old">$9.99</span>
        # Requires careful inspection of the HTML structure around prices.
        # For this specific page, it doesn't seem to have a strikethrough price, so it will be None.

        # 16. 自发货运费 (Self-delivery Shipping Fee) - Highly dynamic and locale-dependent.
        # This typically requires simulating user interaction (e.g., adding to cart, checking shipping)
        # or finding specific shipping cost disclosures. Not reliably available in initial HTML.
        shipping_fee = None  # Placeholder

        # 17. 重定向ID (Redirect ID) - This is the item ID from the URL.
        redirect_id = '9671601485'  # Directly from the URL you provided

        print(f"主图 (Main Image): {main_image}")
        print(f"品牌 (Brand): {brand}")
        print(f"标题 (Title): {title}")
        print(f"评分 (Rating): {rating}")
        print(f"评论数量 (Number of Reviews): {num_reviews}")
        print(f"价格 (Price): {price}")
        print(f"卖家 (Seller): {seller}")
        print(f"配送 (Shipping): {shipping}")
        print(f"变体 (Variants): {variants}")
        print(f"变体ID (Variant ID): {variant_id}")
        print(f"到达时间 (Estimated Delivery Time): {delivery_time}")
        print(f"库存 (Stock): {stock}")
        print(f"类目 (Category): {category}")
        print(f"划线价/原价 (Original Price): {original_price}")
        print(f"自发货运费 (Shipping Fee): {shipping_fee}")
        print(f"重定向ID (Redirect ID): {redirect_id}")

    except requests.exceptions.HTTPError as e:
        print(f"HTTP Error: {e}")
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    main()